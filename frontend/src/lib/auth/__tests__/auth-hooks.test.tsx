/**
 * Comprehensive Authentication Hooks Tests
 * 
 * Tests for useAuth, useRequireAuth, useRequireRole hooks
 * including context management, redirects, and error handling.
 */

import React from 'react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { useRouter } from 'next/navigation';
import { useAuth, AuthContext } from '../useAuth';
import { useRequireAuth } from '../useRequireAuth';
import { useRequireRole } from '../useRequireRole';
import { AuthContextType, UserProfile } from '../types';

// Mock Next.js router
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
}));

const mockPush = vi.fn();
const mockRouter = { push: mockPush };

describe('Authentication Hooks', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (useRouter as any).mockReturnValue(mockRouter);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  // Helper to create mock auth context
  const createMockAuthContext = (overrides: Partial<AuthContextType> = {}): AuthContextType => ({
    user: null,
    session: null,
    profile: null,
    isLoading: false,
    isAuthenticated: false,
    signIn: vi.fn(),
    signOut: vi.fn(),
    signUp: vi.fn(),
    resetPassword: vi.fn(),
    updatePassword: vi.fn(),
    refreshSession: vi.fn(),
    ...overrides,
  });

  // Helper to create mock user profile
  const createMockProfile = (overrides: Partial<UserProfile> = {}): UserProfile => ({
    id: 'user-123',
    auth_id: 'auth-123',
    email: '<EMAIL>',
    role: 'attorney',
    tenant_id: 'tenant-456',
    first_name: 'John',
    last_name: 'Doe',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    ...overrides,
  });

  // Test component that uses useAuth
  const TestAuthComponent = () => {
    const auth = useAuth();
    return (
      <div>
        <div data-testid="loading">{auth.isLoading ? 'Loading' : 'Not Loading'}</div>
        <div data-testid="authenticated">{auth.isAuthenticated ? 'Authenticated' : 'Not Authenticated'}</div>
        <div data-testid="user-email">{auth.user?.email || 'No Email'}</div>
        <div data-testid="profile-role">{auth.profile?.role || 'No Role'}</div>
      </div>
    );
  };

  // Test component that uses useRequireAuth
  const TestRequireAuthComponent = ({ redirectTo }: { redirectTo?: string }) => {
    const { isLoading, isAuthenticated } = useRequireAuth(redirectTo);
    return (
      <div>
        <div data-testid="require-auth-loading">{isLoading ? 'Loading' : 'Not Loading'}</div>
        <div data-testid="require-auth-authenticated">{isAuthenticated ? 'Authenticated' : 'Not Authenticated'}</div>
        <div data-testid="protected-content">Protected Content</div>
      </div>
    );
  };

  // Test component that uses useRequireRole
  const TestRequireRoleComponent = ({ allowedRoles }: { allowedRoles: string[] }) => {
    const { isLoading, isAuthorized } = useRequireRole(allowedRoles);
    return (
      <div>
        <div data-testid="require-role-loading">{isLoading ? 'Loading' : 'Not Loading'}</div>
        <div data-testid="require-role-authorized">{isAuthorized ? 'Authorized' : 'Not Authorized'}</div>
        <div data-testid="role-protected-content">Role Protected Content</div>
      </div>
    );
  };

  describe('useAuth', () => {
    it('should throw error when used outside AuthProvider', () => {
      // Suppress console.error for this test
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      expect(() => {
        render(<TestAuthComponent />);
      }).toThrow('useAuth must be used within an AuthProvider');
      
      consoleSpy.mockRestore();
    });

    it('should return auth context when used within AuthProvider', () => {
      const mockContext = createMockAuthContext({
        isAuthenticated: true,
        user: { id: 'user-123', email: '<EMAIL>' } as any,
        profile: createMockProfile(),
      });

      render(
        <AuthContext.Provider value={mockContext}>
          <TestAuthComponent />
        </AuthContext.Provider>
      );

      expect(screen.getByTestId('loading')).toHaveTextContent('Not Loading');
      expect(screen.getByTestId('authenticated')).toHaveTextContent('Authenticated');
      expect(screen.getByTestId('user-email')).toHaveTextContent('<EMAIL>');
      expect(screen.getByTestId('profile-role')).toHaveTextContent('attorney');
    });

    it('should handle loading state', () => {
      const mockContext = createMockAuthContext({
        isLoading: true,
        isAuthenticated: false,
      });

      render(
        <AuthContext.Provider value={mockContext}>
          <TestAuthComponent />
        </AuthContext.Provider>
      );

      expect(screen.getByTestId('loading')).toHaveTextContent('Loading');
      expect(screen.getByTestId('authenticated')).toHaveTextContent('Not Authenticated');
    });

    it('should handle unauthenticated state', () => {
      const mockContext = createMockAuthContext({
        isLoading: false,
        isAuthenticated: false,
        user: null,
        profile: null,
      });

      render(
        <AuthContext.Provider value={mockContext}>
          <TestAuthComponent />
        </AuthContext.Provider>
      );

      expect(screen.getByTestId('loading')).toHaveTextContent('Not Loading');
      expect(screen.getByTestId('authenticated')).toHaveTextContent('Not Authenticated');
      expect(screen.getByTestId('user-email')).toHaveTextContent('No Email');
      expect(screen.getByTestId('profile-role')).toHaveTextContent('No Role');
    });
  });

  describe('useRequireAuth', () => {
    it('should not redirect when user is authenticated', async () => {
      const mockContext = createMockAuthContext({
        isLoading: false,
        isAuthenticated: true,
      });

      render(
        <AuthContext.Provider value={mockContext}>
          <TestRequireAuthComponent />
        </AuthContext.Provider>
      );

      await waitFor(() => {
        expect(mockPush).not.toHaveBeenCalled();
      });

      expect(screen.getByTestId('require-auth-authenticated')).toHaveTextContent('Authenticated');
    });

    it('should redirect to default login when user is not authenticated', async () => {
      const mockContext = createMockAuthContext({
        isLoading: false,
        isAuthenticated: false,
      });

      render(
        <AuthContext.Provider value={mockContext}>
          <TestRequireAuthComponent />
        </AuthContext.Provider>
      );

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/auth/login');
      });
    });

    it('should redirect to custom path when specified', async () => {
      const mockContext = createMockAuthContext({
        isLoading: false,
        isAuthenticated: false,
      });

      render(
        <AuthContext.Provider value={mockContext}>
          <TestRequireAuthComponent redirectTo="/custom-login" />
        </AuthContext.Provider>
      );

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/custom-login');
      });
    });

    it('should not redirect while loading', async () => {
      const mockContext = createMockAuthContext({
        isLoading: true,
        isAuthenticated: false,
      });

      render(
        <AuthContext.Provider value={mockContext}>
          <TestRequireAuthComponent />
        </AuthContext.Provider>
      );

      // Wait a bit to ensure no redirect happens
      await new Promise(resolve => setTimeout(resolve, 100));
      
      expect(mockPush).not.toHaveBeenCalled();
      expect(screen.getByTestId('require-auth-loading')).toHaveTextContent('Loading');
    });

    it('should return correct loading and authentication states', () => {
      const mockContext = createMockAuthContext({
        isLoading: true,
        isAuthenticated: false,
      });

      render(
        <AuthContext.Provider value={mockContext}>
          <TestRequireAuthComponent />
        </AuthContext.Provider>
      );

      expect(screen.getByTestId('require-auth-loading')).toHaveTextContent('Loading');
      expect(screen.getByTestId('require-auth-authenticated')).toHaveTextContent('Not Authenticated');
    });
  });

  describe('useRequireRole', () => {
    it('should not redirect when user has required role', async () => {
      const mockContext = createMockAuthContext({
        isLoading: false,
        isAuthenticated: true,
        profile: createMockProfile({ role: 'attorney' }),
      });

      render(
        <AuthContext.Provider value={mockContext}>
          <TestRequireRoleComponent allowedRoles={['attorney', 'partner']} />
        </AuthContext.Provider>
      );

      await waitFor(() => {
        expect(mockPush).not.toHaveBeenCalled();
      });

      expect(screen.getByTestId('require-role-authorized')).toHaveTextContent('Authorized');
    });

    it('should redirect to unauthorized when user lacks required role', async () => {
      const mockContext = createMockAuthContext({
        isLoading: false,
        isAuthenticated: true,
        profile: createMockProfile({ role: 'staff' }),
      });

      render(
        <AuthContext.Provider value={mockContext}>
          <TestRequireRoleComponent allowedRoles={['attorney', 'partner']} />
        </AuthContext.Provider>
      );

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/unauthorized');
      });

      expect(screen.getByTestId('require-role-authorized')).toHaveTextContent('Not Authorized');
    });

    it('should not redirect while loading', async () => {
      const mockContext = createMockAuthContext({
        isLoading: true,
        isAuthenticated: true,
        profile: createMockProfile({ role: 'staff' }),
      });

      render(
        <AuthContext.Provider value={mockContext}>
          <TestRequireRoleComponent allowedRoles={['attorney']} />
        </AuthContext.Provider>
      );

      // Wait a bit to ensure no redirect happens
      await new Promise(resolve => setTimeout(resolve, 100));
      
      expect(mockPush).not.toHaveBeenCalled();
      expect(screen.getByTestId('require-role-loading')).toHaveTextContent('Loading');
    });

    it('should not redirect when user is not authenticated', async () => {
      const mockContext = createMockAuthContext({
        isLoading: false,
        isAuthenticated: false,
        profile: null,
      });

      render(
        <AuthContext.Provider value={mockContext}>
          <TestRequireRoleComponent allowedRoles={['attorney']} />
        </AuthContext.Provider>
      );

      // Wait a bit to ensure no redirect happens
      await new Promise(resolve => setTimeout(resolve, 100));
      
      expect(mockPush).not.toHaveBeenCalled();
      expect(screen.getByTestId('require-role-authorized')).toHaveTextContent('Not Authorized');
    });

    it('should handle missing profile gracefully', async () => {
      const mockContext = createMockAuthContext({
        isLoading: false,
        isAuthenticated: true,
        profile: null,
      });

      render(
        <AuthContext.Provider value={mockContext}>
          <TestRequireRoleComponent allowedRoles={['attorney']} />
        </AuthContext.Provider>
      );

      expect(screen.getByTestId('require-role-authorized')).toHaveTextContent('Not Authorized');
    });

    it('should handle multiple allowed roles', async () => {
      const mockContext = createMockAuthContext({
        isLoading: false,
        isAuthenticated: true,
        profile: createMockProfile({ role: 'partner' }),
      });

      render(
        <AuthContext.Provider value={mockContext}>
          <TestRequireRoleComponent allowedRoles={['attorney', 'partner', 'admin']} />
        </AuthContext.Provider>
      );

      await waitFor(() => {
        expect(mockPush).not.toHaveBeenCalled();
      });

      expect(screen.getByTestId('require-role-authorized')).toHaveTextContent('Authorized');
    });
  });
});
